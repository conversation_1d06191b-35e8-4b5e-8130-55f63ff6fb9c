package com.x5.logistics.service.reportinfo

import com.x5.logistics.data.reportinfo.ColumnInfoTable
import com.x5.logistics.data.reportinfo.ReportInfoTable
import com.x5.logistics.rest.dto.reportinfo.ReportColumnInfo
import com.x5.logistics.rest.exception.RecordNotFoundException
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Service

@Service
class ReportInfoService {

    val logger = getLogger()

    suspend fun getColumns(reportName: String): ReportColumnInfo = newSuspendedTransaction {
        logger.info("Get columns for report $reportName")

        val query = ColumnInfoTable.innerJoin(ReportInfoTable)
            .selectAll()
            .where { (ColumnInfoTable.isDisplayed eq true) and (ReportInfoTable.report eq reportName) }
            .orderBy(ColumnInfoTable.order to SortOrder.ASC)

        logger.debug(query.prepareSQL(QueryBuilder(false)))

        val rows = query.toList()
        logger.debug("Query returned ${rows.size} rows")

        if (rows.isEmpty()) {
            throw RecordNotFoundException("Columns for report $reportName not found")
        }

        val reportInfo = rows.first().let {
            Triple(
                it[ReportInfoTable.report],
                it[ReportInfoTable.reportDescription] ?: "",
                it[ReportInfoTable.granularityInfo] ?: ""
            )
        }

        val columnsByName = rows.groupBy { it[ColumnInfoTable.name] }

        val columns = columnsByName.map { (columnName, columnRows) ->
            val infoFields = columnRows.mapNotNull { row ->
                val field = row[ColumnInfoTable.field]
                val content = row[ColumnInfoTable.content]

                if (field != null && content != null) {
                    ReportColumnInfo.ColumnInfoField(field, content)
                } else {
                    null
                }
            }

            ReportColumnInfo.ColumnInfo(
                name = columnName,
                info = infoFields
            )
        }

        logger.debug("Mapped ${columns.size} columns")

        ReportColumnInfo(
            report = reportInfo.first,
            reportDescription = reportInfo.second,
            granularityInfo = reportInfo.third,
            columns = columns
        )
    }
}
