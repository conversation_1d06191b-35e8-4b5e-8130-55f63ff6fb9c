package com.x5.logistics.service.complexvalues

import com.x5.logistics.data.dictionary.BiComplexValueEntity
import com.x5.logistics.data.dictionary.BiComplexValuesTable
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesCreateRequest
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesGetResponse
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesUpdateRequest
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class ComplexValuesService {

    suspend fun getAllAvailableValues(user: String): List<ComplexValuesGetResponse> = newSuspendedTransaction {
        val entities = BiComplexValueEntity.find {
            (BiComplexValuesTable.deleted eq false) and
                    ((BiComplexValuesTable.createdBy eq user) or
                            (BiComplexValuesTable.type eq "public"))
        }.toList()
            .sortedWith(
                compareBy<BiComplexValueEntity> { it.type != "public" }
                    .thenBy { it.name.lowercase() }
            )

        ComplexValuesGetResponse.fromEntities(entities)
    }

    suspend fun getValuesByIds(ids: List<Int>): List<ComplexValuesGetResponse> = newSuspendedTransaction {
        val entities = BiComplexValueEntity.forIds(ids).toList()
            .sortedBy { it.name.lowercase() }

        ComplexValuesGetResponse.fromEntities(entities)
    }

    suspend fun createComplexValue(request: ComplexValuesCreateRequest, username: String): ComplexValuesGetResponse =
        newSuspendedTransaction {
            // Проверка на уникальность имени (без учета регистра)
            val existingWithSameName = BiComplexValueEntity.find {
                (BiComplexValuesTable.deleted eq false) and
                        (BiComplexValuesTable.createdBy eq username) and
                        (BiComplexValuesTable.name.lowerCase() eq request.name.lowercase())
            }.firstOrNull()

            if (existingWithSameName != null) {
                throw WrongRequestDataException("Показатель с таким именем уже существует")
            }

            val now = Instant.now()

            val entity = BiComplexValueEntity.new {
                name = request.name
                type = "private"
                settings = request.settings
                createdBy = username
                createdAt = now
                updatedAt = now
                deleted = false
            }

            ComplexValuesGetResponse.fromEntity(entity)
        }

    suspend fun updateComplexValue(request: ComplexValuesUpdateRequest, username: String): ComplexValuesGetResponse =
        newSuspendedTransaction {
            val existingValue = BiComplexValueEntity.find {
                (BiComplexValuesTable.id eq request.id) and
                        (BiComplexValuesTable.createdBy eq username) and
                        (BiComplexValuesTable.deleted eq false)
            }.firstOrNull()
                ?: throw WrongRequestDataException("Показатель не найден или не принадлежит пользователю")

            val existingWithSameName = BiComplexValueEntity.find {
                (BiComplexValuesTable.deleted eq false) and
                        (BiComplexValuesTable.createdBy eq username) and
                        (BiComplexValuesTable.name.lowerCase() eq request.name.lowercase()) and
                        (BiComplexValuesTable.id neq request.id)
            }.firstOrNull()

            if (existingWithSameName != null) {
                throw WrongRequestDataException("Показатель с таким именем уже существует")
            }

            existingValue.name = request.name
            existingValue.settings = request.settings
            existingValue.updatedAt = Instant.now()

            ComplexValuesGetResponse.fromEntity(existingValue)
        }

    suspend fun deleteComplexValue(id: Int, username: String): Unit = newSuspendedTransaction {
        val existingValue = BiComplexValueEntity.find {
            (BiComplexValuesTable.id eq id) and
                    (BiComplexValuesTable.createdBy eq username) and
                    (BiComplexValuesTable.deleted eq false)
        }.firstOrNull()
            ?: throw WrongRequestDataException("Показатель не найден или не принадлежит пользователю")

        existingValue.deleted = true
        existingValue.updatedAt = Instant.now()
    }

}
