package com.x5.logistics.service.som

import com.x5.logistics.data.som.CharacterDeliveryEntity
import com.x5.logistics.rest.dto.som.SomWidgetsDictionaryResponse
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.stereotype.Service

@Service
class SomWidgetsService {

    suspend fun getDictionary(): SomWidgetsDictionaryResponse = newSuspendedTransaction {
        SomWidgetsDictionaryResponse(
            characters = CharacterDeliveryEntity.all()
                .mapNotNull { it.name }
                .distinct()
        )
    }
}