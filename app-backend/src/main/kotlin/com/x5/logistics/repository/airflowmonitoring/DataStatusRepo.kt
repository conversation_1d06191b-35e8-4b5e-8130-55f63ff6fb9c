package com.x5.logistics.repository.airflowmonitoring

import com.x5.logistics.data.airflowmonitoring.AllTablesInfo
import com.x5.logistics.data.airflowmonitoring.AllTablesUpdateLog
import com.x5.logistics.repository.castToDate
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.max
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class DataStatusRepo {
    val logger = getLogger()

    fun getLastUpdateDate(): LocalDate = transaction {
        val tableNameSubQuery = AllTablesInfo.select(AllTablesInfo.fullTableName)
            .where { AllTablesInfo.isUpdatedByAirflow eq true }

        val maxLastUpdateDateAlias = AllTablesUpdateLog.updateStartAt.max().castToDate().alias("last_stast_dt")
        val maxLastUpdateDateSubQuery = with(AllTablesUpdateLog) {
            select(maxLastUpdateDateAlias, logTableName)
                .where { logTableName inSubQuery tableNameSubQuery }
                .groupBy(logTableName).alias("query")
        }

        val lastUpdateDateAlias = Min(maxLastUpdateDateAlias.aliasOnlyExpression(), JavaLocalDateColumnType()).alias("date")

        maxLastUpdateDateSubQuery
            .select(lastUpdateDateAlias)
            .also { logger.debug(it.prepareSQL(QueryBuilder(false))) }
            .first()[lastUpdateDateAlias]!!
    }
}