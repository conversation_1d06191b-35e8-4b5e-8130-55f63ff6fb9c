package com.x5.logistics.rest.controller.complexvalues

import com.x5.logistics.rest.dto.complexvalues.ComplexValuesCreateRequest
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesGetResponse
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesUpdateRequest
import com.x5.logistics.service.complexvalues.ComplexValuesService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.name
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.server.ResponseStatusException

@RestController
@RequestMapping("/api/bi/complex-value")
class ComplexValuesController(
    private val service: ComplexValuesService
) {
    @Tag(name = "Сложные показатели")
    @Operation(summary = "Получение сложных показателей")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ"
            ),
            ApiResponse(
                responseCode = "400",
                description = "Запрошены только несуществующие ID"
            )
        ]
    )
    @GetMapping
    suspend fun getComplexValues(
        token: JwtToken?,
        @RequestParam(required = false) id: List<Int>?
    ): List<ComplexValuesGetResponse> {
        return if (id == null) {
            // Возвращаем все неудалённые публичные показатели и неудалённые личные показатели юзера
            service.getAllAvailableValues(token.name)
        } else {
            // Возвращаем показатели по указанным ID
            val values = service.getValuesByIds(id)
            if (values.isEmpty() && id.isNotEmpty()) {
                throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Запрошены только несуществующие ID")
            }
            values
        }
    }

    @Tag(name = "Сложные показатели")
    @Operation(summary = "Создание сложного показателя")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ"
            ),
            ApiResponse(
                responseCode = "400",
                description = "Ошибка валидации"
            )
        ]
    )
    @PostMapping
    suspend fun createComplexValue(
        token: JwtToken?,
        @Valid @RequestBody request: ComplexValuesCreateRequest
    ): ResponseEntity<ComplexValuesGetResponse> {
        val response = service.createComplexValue(request, token.name)
        return ResponseEntity.ok(response)
    }

    @Tag(name = "Сложные показатели")
    @Operation(summary = "Редактирование сложного показателя")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ"
            ),
            ApiResponse(
                responseCode = "400",
                description = "Ошибка валидации"
            )
        ]
    )
    @PutMapping
    suspend fun updateComplexValue(
        token: JwtToken?,
        @Valid @RequestBody request: ComplexValuesUpdateRequest
    ): ResponseEntity<ComplexValuesGetResponse> {
        val response = service.updateComplexValue(request, token.name)
        return ResponseEntity.ok(response)
    }

    @Tag(name = "Сложные показатели")
    @Operation(summary = "Удаление сложного показателя")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ"
            ),
            ApiResponse(
                responseCode = "400",
                description = "Ошибка валидации"
            )
        ]
    )
    @DeleteMapping("/{id}")
    suspend fun deleteComplexValue(
        token: JwtToken?,
        @PathVariable id: Int
    ): ResponseEntity<Void> {
        service.deleteComplexValue(id, token.name)
        return ResponseEntity.ok().build()
    }
}
