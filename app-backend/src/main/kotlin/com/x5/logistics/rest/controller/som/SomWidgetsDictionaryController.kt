package com.x5.logistics.rest.controller.som

import com.x5.logistics.rest.dto.som.SomWidgetsDictionaryResponse
import com.x5.logistics.service.som.SomWidgetsService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(value = ["/api/somwidgets"])
class SomWidgetsDictionaryController(
    val service: SomWidgetsService
) {

    private val log = getLogger()

    @Tag(name = "Виджеты СОМ")
    @Operation(summary = "Получение словаря для виджетов СОМ")
    @ApiResponses(
        value = [ApiResponse(
            responseCode = "200", description = "Успешный ответ", content = [
                Content(schema = Schema(implementation = SomWidgetsDictionaryResponse::class))
            ]
        )]
    )
    @GetMapping("/dictionary")
    suspend fun getDictionary(): SomWidgetsDictionaryResponse {
        return service.getDictionary()
    }
}