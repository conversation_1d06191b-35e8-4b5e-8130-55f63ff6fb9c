package com.x5.logistics.rest.dto.complexvalues

import com.x5.logistics.data.dictionary.BiComplexSettings
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

data class ComplexValuesCreateRequest(
    @field:NotBlank(message = "Имя показателя не может быть пустым")
    @field:Size(max = 50, message = "Имя показателя не должно превышать 50 символов")
    val name: String,
    
    @field:Valid
    val settings: BiComplexSettings
)