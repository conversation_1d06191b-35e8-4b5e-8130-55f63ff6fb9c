package com.x5.logistics.rest.controller.reportinfo

import com.x5.logistics.rest.dto.reportinfo.ReportColumnInfo
import com.x5.logistics.service.reportinfo.ReportInfoService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/report-info")
class ReportInfoController(
    private val service: ReportInfoService
) {

    //GET /api/report-info/columns?report={reportname}
    @GetMapping("/columns")
    suspend fun getReportColumns(@RequestParam("report") reportName: String): ReportColumnInfo {
        return service.getColumns(reportName)
    }
}