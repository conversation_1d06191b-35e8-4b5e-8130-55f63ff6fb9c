package com.x5.logistics.rest.dto.complexvalues

import com.x5.logistics.data.dictionary.BiComplexSettings
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.Size

data class ComplexValuesUpdateRequest(
    @field:NotNull(message = "ID показателя не может быть пустым")
    @field:Positive(message = "ID показателя должен быть положительным числом")
    val id: Int,
    
    @field:NotBlank(message = "Имя показателя не может быть пустым")
    @field:Size(min = 1, max = 50, message = "Имя показателя должно содержать от 1 до 50 символов")
    val name: String,
    
    @field:Valid
    val settings: BiComplexSettings
)
