package com.x5.logistics.rest.dto.reportinfo

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Информация об отчете и его колонках")
data class ReportColumnInfo(
    @Schema(description = "Название отчета")
    val report: String,

    @Schema(description = "Подсказка к отчету")
    val reportDescription: String,

    @Schema(description = "Подсказка к полю \"Гранулярность\"")
    val granularityInfo: String,

    @Schema(description = "Массив данных по колонкам отчета")
    val columns: List<ColumnInfo>
) {
    @Schema(description = "Информация о колонке отчета")
    data class ColumnInfo(
        @Schema(description = "Колонка отчета")
        val name: String,

        @Schema(description = "Массив данных по содержимому подсказки")
        val info: List<ColumnInfoField>
    )

    @Schema(description = "Поле информации о колонке")
    data class ColumnInfoField(
        @Schema(description = "Название раздела подсказки")
        val field: String,

        @Schema(description = "Содержимое подсказки в разделе")
        val content: String
    )
}