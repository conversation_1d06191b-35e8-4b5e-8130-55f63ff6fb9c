package com.x5.logistics.rest.exception

import com.x5.logistics.util.getLogger
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.WebRequest
import org.springframework.web.server.ResponseStatusException
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.util.*

@ControllerAdvice
class RestExceptionHandler : ResponseEntityExceptionHandler() {
    private val log = getLogger()

    @ExceptionHandler(value = [(Exception::class)])
    fun global(e: Exception, request: WebRequest): ResponseEntity<Any> {
        val id = UUID.randomUUID().toString()
        val rootElement = e.stackTrace.asSequence()
            .firstOrNull { it.className.startsWith("com.x5.logistics") }
        val fileName = rootElement?.fileName
        val lineNumber = rootElement?.lineNumber
        val codeForUser = rootElement?.className?.split(".")
            ?.takeLast(3)
            ?.map { path -> path.hashCode().toByte().let { if (it < 0) -it else it } }
            ?.joinToString(".") + ".$lineNumber:$id"
        log.error("$fileName:$lineNumber id: $id\n {}", e.stackTraceToString())
        return handleExceptionInternal(e, codeForUser, HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, request)
    }

    @ExceptionHandler(value = [(WrongRequestDataException::class)])
    fun notFound(e: WrongRequestDataException, request: WebRequest): ResponseEntity<Any> {
        log.warn("WrongRequestDataException. [message={}]", e.message, e)
        return handleExceptionInternal(e, e.message, HttpHeaders(), HttpStatus.BAD_REQUEST, request)
    }

    @ExceptionHandler(value = [(RecordAlreadyExistsException::class)])
    fun alreadyExists(e: RecordAlreadyExistsException, request: WebRequest): ResponseEntity<Any> {
        log.warn("RecordAlreadyExistsException. [message={}]", e.message, e)
        return handleExceptionInternal(e, e.message, HttpHeaders(), HttpStatus.BAD_REQUEST, request)
    }

    @ExceptionHandler(IllegalArgumentException::class)
    fun handleIllegalArgumentException(ex: IllegalArgumentException): ResponseEntity<Map<String, String>> {
        ex.printStackTrace()
        return ResponseEntity.badRequest().body(mapOf("error" to ex.message!!))
    }

    @ExceptionHandler(RecordNotFoundException::class)
    fun handleRecordNotFoundException(ex: RecordNotFoundException): ResponseEntity<Map<String, String>> {
        ex.printStackTrace()
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(mapOf("error" to ex.message!!))
    }
}
