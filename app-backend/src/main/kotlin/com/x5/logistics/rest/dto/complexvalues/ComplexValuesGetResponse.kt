package com.x5.logistics.rest.dto.complexvalues

import com.x5.logistics.data.dictionary.BiComplexSettings
import com.x5.logistics.data.dictionary.BiComplexValueEntity

data class ComplexValuesGetResponse(
    val id: Int,
    val name: String,
    val settings: BiComplexSettings,
    val type: String,
    val owner: String,
    val updatedAt: String,
    val deleted: Boolean
) {
    companion object {
        fun fromEntity(entity: BiComplexValueEntity): ComplexValuesGetResponse {
            return ComplexValuesGetResponse(
                id = entity.id.value,
                name = entity.name,
                settings = entity.settings,
                type = entity.type,
                owner = entity.createdBy,
                updatedAt = entity.updatedAt.toString(),
                deleted = entity.deleted
            )
        }
        
        fun fromEntities(entities: List<BiComplexValueEntity>): List<ComplexValuesGetResponse> {
            return entities.map { fromEntity(it) }
        }
    }
}
