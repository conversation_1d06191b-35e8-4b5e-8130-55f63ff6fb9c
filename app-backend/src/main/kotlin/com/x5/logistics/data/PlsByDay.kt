package com.x5.logistics.data

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object PlsByDay : Table("pls_by_day") {
    val mvz = text("mvz_id").nullable()
    val mvzName = text("mvz_name").nullable()
    val vehicleDate = date("vehicle_date").nullable()
    val vehicleLicense = text("vehicle_license").nullable()
    val tsGroup = text("ts_group").nullable()
    val tsType = text("ts_type").nullable()
    val marka = text("marka").nullable()
    val model = text("model").nullable()
    val loadWgt = double("load_wgt").nullable()
    val year = integer("year").nullable()
    val createDate = date("create_date").nullable()
    val vin = text("fleet_num").nullable()
    val equnr = long("equnr").nullable()
    val gboText = text("gbo_text").nullable()
    val noCompart = integer("no_compart").nullable()
    val trailerLicenseNum = text("trailer_license_num").nullable()
    val mileage = double("mileage").nullable()
    val plTonnage = double("pl_tonnage").nullable()
    val qmnum = text("qmnum").nullable()
}