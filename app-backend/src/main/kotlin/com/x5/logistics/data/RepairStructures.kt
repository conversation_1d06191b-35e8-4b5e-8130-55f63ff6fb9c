package com.x5.logistics.data

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairStructures : Table("repair_structures") {
    val repairStartDate = date("repair_start_date").nullable()
    val vehicleMvz = text("vehicle_mvz").nullable()
    val vehicleMvzName = text("vehicle_mvz_name").nullable()
    val structureName = text("structure_name").nullable()
    val repairPlace = text("repair_place").nullable()
    val vrt = text("vrt").nullable()
    val vrtName = text("vrt_name").nullable()
    val eventId = text("event_id").nullable()
    val eventText = text("event_text").nullable()
    val structureExpense = double("structure_expense").nullable()
    val plQmnum = text("pl_qmnum").nullable()
    val trailerLicenseNum = text("trailer_license_num").nullable()
    val headLicense = text("head_license").nullable()
    val headMarka = text("head_marka").nullable()
    val headModel = text("head_model").nullable()
    val headCreateDate = date("head_create_date").nullable()
    val headEqunr = long("head_equnr").nullable()
    val headFleetNum = text("head_fleet_num").nullable()
    val headGboText = text("head_gbo_text").nullable()
    val headGroup = text("head_group").nullable()
    val vehicleTonnage = double("pl_tonnage").nullable()
    val headNoCompart = integer("head_no_compart").nullable()
    val headType = text("head_type").nullable()
    val headYear = integer("head_year").nullable()
}