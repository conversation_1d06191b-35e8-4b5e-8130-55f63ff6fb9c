package com.x5.logistics.data.dictionary

import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size

data class BiComplexSettings(
    @field:NotBlank(message = "Выражение не может быть пустым")
    val expression: String,
    
    @field:NotNull(message = "Тип значения не может быть пустым")
    val valueType: ValueType,
    
    @field:NotEmpty(message = "Должен быть хотя бы один параметр")
    @field:Size(min = 2, message = "Должно быть не менее двух параметров")
    @field:Valid
    val parameters: List<BiComplexParameter>
)

data class BiComplexParameter(
    @field:NotBlank(message = "Имя параметра не может быть пустым")
    val name: String,
    
    @field:NotNull(message = "Функция агрегации не может быть пустой")
    val function: AggregationFunction,
    
    @field:Valid
    val filters: List<BiComplexFilter> = emptyList()
)

data class BiComplexFilter(
    @field:NotBlank(message = "Имя фильтра не может быть пустым")
    val name: String,
    
    @field:NotBlank(message = "Условие фильтрации не может быть пустым")
    val condition: String,
    
    val values: List<String> = emptyList()
)

enum class ValueType {
    value, share
}

enum class AggregationFunction {
    count, countDistinct, sum, avg, min, max
}