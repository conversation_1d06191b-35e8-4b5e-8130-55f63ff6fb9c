package com.x5.logistics.data.reportinfo

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

object ColumnInfoTable : IntIdTable("column_info") {
    val report = reference("report", ReportInfoTable)
    val name = text("name")
    val field = text("field").nullable()
    val content = text("content").nullable()
    val isDisplayed = bool("is_displayed").default(true)
    val order = integer("order").nullable()
}

class ColumnInfoEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<ColumnInfoEntity>(ColumnInfoTable)

    var report by ReportInfoEntity referencedOn ColumnInfoTable.report
    var name by ColumnInfoTable.name
    var field by ColumnInfoTable.field
    var content by ColumnInfoTable.content
    var isDisplayed by ColumnInfoTable.isDisplayed
    var order by ColumnInfoTable.order
}