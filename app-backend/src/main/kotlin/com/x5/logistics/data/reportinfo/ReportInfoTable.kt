package com.x5.logistics.data.reportinfo

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

object ReportInfoTable : IntIdTable("report_info") {
    val report = text("report")
    val reportDescription = text("report_description").nullable()
    val granularityInfo = text("granularity_info").nullable()
}

class ReportInfoEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<ReportInfoEntity>(ReportInfoTable)

    var report by ReportInfoTable.report
    var reportDescription by ReportInfoTable.reportDescription
    var granularityInfo by ReportInfoTable.granularityInfo
}