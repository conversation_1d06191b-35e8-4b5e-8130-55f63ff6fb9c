package com.x5.logistics.data.dictionary

import com.x5.logistics.repository.jsonb
import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.javatime.timestamp

object BiComplexValuesTable : IntIdTable("ts.bi_complex_values") {
    val name = varchar("name", length = 255)
    val type = varchar("type", length = 255)
    val settings = jsonb<BiComplexSettings>("settings")
    val createdBy = varchar("created_by", length = 255)
    val createdAt = timestamp("created_at")
    val updatedAt = timestamp("updated_at")
    val deleted = bool("deleted")
}

class BiComplexValueEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<BiComplexValueEntity>(BiComplexValuesTable)

    var name by BiComplexValuesTable.name
    var type by BiComplexValuesTable.type
    var settings by BiComplexValuesTable.settings
    var createdBy by BiComplexValuesTable.createdBy
    var createdAt by BiComplexValuesTable.createdAt
    var updatedAt by BiComplexValuesTable.updatedAt
    var deleted by BiComplexValuesTable.deleted
}
