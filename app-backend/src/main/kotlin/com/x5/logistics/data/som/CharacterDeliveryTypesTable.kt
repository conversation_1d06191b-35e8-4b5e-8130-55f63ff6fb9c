package com.x5.logistics.data.som

import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable

object CharacterDeliveryTypesTable : IntIdTable("character_delivery_types") {
    val name = text("name").nullable()
}

class CharacterDeliveryEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<CharacterDeliveryEntity>(CharacterDeliveryTypesTable)

    var name by CharacterDeliveryTypesTable.name
}