--liquibase formatted sql

--changeset igor.belog<PERSON><PERSON>@x5.ru:2306279.1
UPDATE ts.user_setting
SET value = jsonb_set(
        value::jsonb,
        '{lg}',
        (
            SELECT jsonb_agg(elem)
            FROM jsonb_array_elements(value::jsonb->'lg') AS elem
            WHERE elem->>'i' != 'TT_hours'
        )
            )
WHERE key = 'dashboard'
  AND value::jsonb->'lg' IS NOT NULL
  AND EXISTS (
    SELECT 1
    FROM jsonb_array_elements(value::jsonb->'lg') AS elem
    WHERE elem->>'i' = 'TT_hours'
);

