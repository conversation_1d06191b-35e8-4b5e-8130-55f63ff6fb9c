--liquibase formatted sql

--changeset igor.belog<PERSON><PERSON>@x5.ru:2237778_1

create table if not exists ts.report_info
(
    id                 bigserial not null primary key,
    report             varchar   not null,
    report_description varchar,
    granularity_info   varchar
);

create table if not exists ts.column_info
(
    id           bigserial not null primary key,
    report       bigint    not null,
    name         varchar   not null,
    field        varchar,
    content      varchar,
    is_displayed boolean   not null default true,
    "order"      int4,
    foreign key (report) references ts.report_info (id)
)

--rollback drop table if exists column_info
--rollback drop table if exists report_info
