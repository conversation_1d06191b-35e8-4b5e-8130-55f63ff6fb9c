<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2237763.2" author="ayzhan.zheksembek">
        <sql><![CDATA[
            UPDATE ts.report_templates
            SET settings = '
{
    "sort": [
    ],
    "columns": [
         {
            "label": "Гос. номер ТС",
            "name": "vehicleLicense",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Вид ТС",
            "name": "vehicleGroup",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Тип ТС",
            "name": "vehicleType",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Марка",
            "name": "vehicleBrand",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Модель",
            "name": "vehicleModel",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Тоннаж",
            "name": "vehicleTonnage",
            "isPinned": true,
            "isVisible": true
        },
        {
            "label": "Год выпуска",
            "name": "vehicleYear",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Дата ввода в эксплуатацию",
            "name": "vehicleCreateDate",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "VIN номер",
            "name": "vehicleVin",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Единица оборудования",
            "name": "equnr",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Структура затрат",
            "name": "structureName",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Место ремонта",
            "name": "repairPlace",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Вид ремонта",
            "name": "reqType",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Подвид ремонта",
            "name": "reqSubtype",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Код ВРТ",
            "name": "vrt",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "ВРТ",
            "name": "vrtName",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Код события",
            "name": "eventId",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Событие",
            "name": "eventText",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "ГБО",
            "name": "gbo",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Паллетовместимость",
            "name": "compartAmount",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Гос. номер прицепа",
            "name": "trailerLicenseNum",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Территория ",
            "name": "ter",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Макрорегион ",
            "name": "mr",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "АТП",
            "name": "atp",
            "isPinned": true,
            "isVisible": true
        },
        {
            "label": "МВЗ",
            "name": "mvz",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Название МВЗ",
            "name": "mvzName",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Вид деятельности АТП",
            "name": "atpType",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Тип МВЗ",
            "name": "mvzType",
            "isPinned": false,
            "isVisible": false
        },
        {
            "label": "Пробег за период, км",
            "name": "mileage",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "План. затраты, руб",
            "name": "repairExpensesFullPlan",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Затраты, руб",
            "name": "repairExpensesFull",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Отклонение затрат от плана, руб",
            "name": "repairExpensesFullDeviation",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Факт, руб/км",
            "name": "repairRubFact",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Тариф, руб/км",
            "name": "repairRubPlan",
            "isPinned": false,
            "isVisible": true
        },
        {
            "label": "Отклонение факта от тарифа, руб/км",
            "name": "repairRubDeviation",
            "isPinned": false,
            "isVisible": true
        }
        ],
            "filters": []
        }
            '
            , update_time = current_timestamp
            WHERE report = 'REPAIRCOSTFACTOR' AND type = 'default' and user = 'system';

select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'МВЗ ТС', '"МВЗ"');
select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'Тип МВЗ ТС', '"Тип МВЗ"');
select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'Название МВЗ ТС', '"Название МВЗ"');
select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'Пробег на период, км', '"Пробег за период, км"');
select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'Затраты на ремонты, руб', '"Затраты, руб"');
select update_report_templates_settings_label('REPAIRCOSTFACTOR', 'Затраты на ремонты, руб/км', '"Факт, руб/км"');

        ]]></sql>
    </changeSet>
</databaseChangeLog>
