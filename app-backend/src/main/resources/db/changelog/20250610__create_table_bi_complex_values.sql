--liquibase formatted sql

--changeset igor.be<PERSON><PERSON><PERSON>@x5.ru:2370582_1

CREATE TABLE IF NOT EXISTS ts.bi_complex_values
(
    id         SERIAL PRIMARY KEY,
    name       VA<PERSON>HAR   NOT NULL,
    type       VA<PERSON>HAR   NOT NULL,
    settings   JSONB     NOT NULL,
    created_by VA<PERSON><PERSON>R   NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    deleted    BOOLEAN   NOT NULL
);

--rollback DROP TABLE ts.bi_complex_values;