--liquibase formatted sql

--changeset anton.pod<PERSON><PERSON><PERSON><PERSON>@x5.ru:2306237.0

drop  MATERIALIZED view if EXISTS ts.pls_by_day_with_rate;
drop VIEW if exists ts.detailed_report_daily_timeline; -- Это удаляем без восстановления
drop VIEW if exists ts.chart_ktg_joined_view;
drop VIEW if exists ts.chart_repair_stats_joined_view;
drop VIEW if exists ts.chart_ts_number_joined_view; -- Это удаляем без восстановления
drop VIEW if exists ts.hr_mechanic_org_unit_view;
drop VIEW if exists ts.hr_mechanic_timeline; -- Это удаляем без восстановления
drop VIEW if exists ts.ktg_detailed_export_view;
drop VIEW if exists ts.nh_timeline_org_units_view; -- Это удаляем без восстановления
drop VIEW if exists ts.nh_timeline_with_regions;
drop VIEW if exists ts.repair_chart_joined_view; -- Это удаляем без восстановления
drop VIEW if exists ts.repair_detailed_export;
drop VIEW if exists ts.repair_detailed_view;
drop VIEW if exists ts.repair_expenses_by_order_and_vehicle;
drop VIEW if exists ts.repair_org_unit_timeline_joined_view;
drop VIEW if exists ts.repair_rates_view; -- тут новая версия кода
drop VIEW if exists ts.repair_resources_view;
drop VIEW if exists ts.repair_services_view;
drop VIEW if exists ts.repair_services_with_rephops_view;
drop VIEW if exists ts.repair_stats_org_ut_view;
drop VIEW if exists ts.repshop_properties_timeline;
drop VIEW if exists ts.vehicle_daily_timeline;
drop VIEW if exists ts.vehicle_org_units_timeline;
drop VIEW if exists ts.vehicles_full_view;

drop MATERIALIZED VIEW ts.organizational_units_timeline;

CREATE MATERIALIZED VIEW ts.organizational_units_timeline
    TABLESPACE pg_default
AS
WITH all_dates AS (
    SELECT DISTINCT _.start_date
    FROM ( SELECT mvz_log.start_date
           FROM ts.mvz_log
           WHERE NOT mvz_log.deleted
           UNION
           SELECT atp_log.start_date
           FROM ts.atp_log
           WHERE NOT atp_log.deleted
           UNION
           SELECT mr_log.start_date
           FROM ts.mr_log
           WHERE NOT mr_log.deleted
           UNION
           SELECT atp_repshops_log.start_date
           FROM ts.atp_repshops_log
           WHERE NOT atp_repshops_log.deleted
           UNION
           SELECT hr_atp_places_log.start_date
           FROM ts.hr_atp_places_log
           WHERE NOT hr_atp_places_log.deleted
           UNION
           SELECT hr_places_tplaces_log.start_date
           FROM ts.hr_places_tplaces_log
           WHERE NOT hr_places_tplaces_log.deleted) _
    ORDER BY _.start_date
), mvz_log AS (
    SELECT mvz_log.uid AS mvz_id,
           mvz_codes.name AS mvz_name,
           mvz_codes.type AS mvz_type,
           mvz_codes.ut,
           mvz_log.start_date,
           lead(mvz_log.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY mvz_log.uid ORDER BY mvz_log.start_date) AS end_date,
           mvz_log.atp_id,
           atp.name AS atp_name,
           atp.type AS atp_type,
           atp.retail_network
    FROM ts.mvz_log
             JOIN ts.mvz_codes ON mvz_codes.uid::text = mvz_log.uid::text
             JOIN ts.atp ON mvz_log.atp_id = atp.id
    WHERE NOT mvz_log.deleted AND NOT atp.deleted
), atp_log AS (
    SELECT atp_log.atp_id,
           atp_log.start_date,
           lead(atp_log.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY atp_log.atp_id ORDER BY atp_log.start_date) AS end_date,
           atp_log.mr_id,
           region.name AS mr_name
    FROM ts.atp_log atp_log
             LEFT JOIN ts.macro_region region ON atp_log.mr_id = region.id
    WHERE NOT atp_log.deleted AND NOT region.deleted
), mr_log AS (
    SELECT mr_log.mr_id,
           mr_log.start_date,
           lead(mr_log.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY mr_log.mr_id ORDER BY mr_log.start_date) AS end_date,
           mr_log.ter_id AS territory_id,
           t.name AS territory_name
    FROM ts.mr_log
             LEFT JOIN ts.territory t ON t.id = mr_log.ter_id
    WHERE NOT mr_log.deleted AND NOT t.deleted
), repshops_log AS (
    SELECT rslog.atp_id,
           rslog.rs_id AS repshop_id,
           repshops.name AS repshop_name,
           rslog.start_date,
           lead(rslog.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY rslog.atp_id ORDER BY rslog.start_date) AS end_date
    FROM ts.atp_repshops_log rslog
             JOIN ts.repshops ON repshops.id = rslog.rs_id
    WHERE NOT rslog.deleted AND NOT repshops.deleted
), hr_places_log AS (
    SELECT hr_atp_places_log.atp_id,
           hr_atp_places_log.hrp_id AS hr_place_id,
           hr_places.name AS hr_place_name,
           hr_atp_places_log.start_date,
           lead(hr_atp_places_log.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY hr_atp_places_log.atp_id ORDER BY hr_atp_places_log.start_date) AS end_date
    FROM ts.hr_atp_places_log
             JOIN ts.hr_places ON hr_places.id = hr_atp_places_log.hrp_id
    WHERE NOT hr_atp_places_log.deleted AND NOT hr_places.deleted
), places_tplaces_log AS (
    SELECT hr_places_tplaces_log.p_id AS hr_place_id,
           hr_places_tplaces_log.tp_id AS hr_tplace_id,
           hr_total_places.name AS hr_tplace_name,
           hr_places_tplaces_log.start_date,
           lead(hr_places_tplaces_log.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY hr_places_tplaces_log.p_id ORDER BY hr_places_tplaces_log.start_date) AS end_date
    FROM ts.hr_places_tplaces_log
             JOIN ts.hr_places ON hr_places.id = hr_places_tplaces_log.p_id
             JOIN ts.hr_total_places ON hr_total_places.id = hr_places_tplaces_log.tp_id
    WHERE NOT hr_places_tplaces_log.deleted AND NOT hr_places.deleted AND NOT hr_total_places.deleted
), main_data AS (
    SELECT min(_.start_date) AS start_date,
           _.mvz_id,
           _.mvz_name,
           _.mvz_type,
           _.atp_id,
           _.atp_name,
           _.retail_network,
           _.atp_type,
           _.mr_id,
           _.mr_name,
           _.repshop_id,
           _.repshop_name,
           _.hr_place_id,
           _.hr_place_name,
           _.hr_tplace_id,
           _.hr_tplace_name,
           _.territory_id,
           _.territory_name
    FROM ( SELECT ad.start_date,
                  ml.mvz_id,
                  ml.mvz_name,
                  ml.mvz_type,
                  ml.atp_id,
                  ml.atp_name,
                  ml.retail_network,
                  ml.atp_type,
                  al.mr_id,
                  al.mr_name,
                  rl.repshop_id,
                  rl.repshop_name,
                  hrl.hr_place_id,
                  hrl.hr_place_name,
                  hrtpl.hr_tplace_id,
                  hrtpl.hr_tplace_name,
                  mrl.territory_id,
                  mrl.territory_name
           FROM all_dates ad
                    LEFT JOIN mvz_log ml ON ml.start_date <= ad.start_date AND ad.start_date < ml.end_date
                    LEFT JOIN atp_log al ON ml.atp_id = al.atp_id AND al.start_date <= ad.start_date AND ad.start_date < al.end_date
                    LEFT JOIN mr_log mrl ON mrl.mr_id = al.mr_id AND mrl.start_date <= ad.start_date AND ad.start_date < mrl.end_date
                    LEFT JOIN repshops_log rl ON rl.atp_id = ml.atp_id AND rl.start_date <= ad.start_date AND ad.start_date < rl.end_date
                    LEFT JOIN hr_places_log hrl ON hrl.atp_id = ml.atp_id AND hrl.start_date <= ad.start_date AND ad.start_date < hrl.end_date
                    LEFT JOIN places_tplaces_log hrtpl ON hrl.hr_place_id = hrtpl.hr_place_id AND hrtpl.start_date <= ad.start_date AND ad.start_date < hrtpl.end_date) _
    GROUP BY _.mvz_id, _.mvz_name, _.mvz_type, _.atp_id, _.atp_name, _.retail_network, _.atp_type, _.mr_id, _.mr_name, _.repshop_id, _.repshop_name, _.hr_place_id, _.hr_place_name, _.hr_tplace_id, _.hr_tplace_name, _.territory_id, _.territory_name
)
SELECT main_data.start_date,
       lead(main_data.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY main_data.mvz_id ORDER BY main_data.start_date) AS end_date,
       main_data.mvz_id,
       main_data.mvz_name,
       coalesce(main_data.mvz_type, 'Не определено')  as mvz_type,
       coalesce(main_data.atp_id, -1) as atp_id,
       coalesce(main_data.atp_name, 'Не определено') as atp_name,
       coalesce(main_data.retail_network, 'Не определено') as retail_network,
       coalesce(main_data.atp_type, 'Не определено') as atp_type,
       coalesce(main_data.mr_id, -1) as mr_id,
       coalesce(main_data.mr_name, 'Не определено') as mr_name,
       coalesce(main_data.repshop_id, -1) as repshop_id,
       coalesce(main_data.repshop_name, 'Не определено') as repshop_name,
       coalesce(main_data.hr_place_id, -1) as hr_place_id,
       coalesce(main_data.hr_place_name, 'Не определено') as hr_place_name,
       coalesce(main_data.hr_tplace_id, -1) as hr_tplace_id,
       coalesce(main_data.hr_tplace_name, 'Не определено') as hr_tplace_name,
       coalesce(main_data.territory_id, -1) as territory_id,
       coalesce(main_data.territory_name, 'Не определено') as territory_name
FROM main_data
WITH DATA;

CREATE INDEX  IF NOT exists idx_organizational_units_timeline_date_mvz ON ts.organizational_units_timeline USING btree (start_date, end_date, mvz_id);

CREATE OR REPLACE VIEW ts.chart_ktg_joined_view
AS SELECT k.vehicle_date,
          COALESCE(k.equnr::text, 'N/A'::text) AS equnr,
          COALESCE(k.mvz_id, 'N/A'::character varying) AS mvz_id,
          COALESCE(k.mvz_name, 'N/A'::character varying) AS mvz_name,
          o.atp_id,
          COALESCE(o.atp_name, 'N/A'::character varying) AS atp_name,
          o.territory_id,
          COALESCE(o.territory_name, 'N/A'::character varying) AS territory_name,
          o.mr_id,
          COALESCE(o.mr_name, 'N/A'::character varying) AS mr_name,
          COALESCE(o.atp_type, 'N/A'::character varying) AS atp_type,
          COALESCE(o.retail_network, 'N/A'::character varying) AS retail_network,
          COALESCE(o.mvz_type, 'N/A'::character varying) AS mvz_type,
          o.repshop_id,
          COALESCE(o.repshop_name, 'N/A'::character varying) AS repshop_name,
          COALESCE(k.vehicle_group, 'N/A'::character varying) AS vehicle_group,
          COALESCE(k.vehicle_type, 'N/A'::character varying) AS vehicle_type,
          COALESCE(k.vehicle_brand, 'N/A'::character varying) AS vehicle_brand,
          COALESCE(k.vehicle_model, 'N/A'::character varying) AS vehicle_model,
          COALESCE(k.vehicle_create_year::text, 'N/A'::text) AS vehicle_create_year,
          COALESCE(to_char(k.vehicle_tonnage, 'FM90D0'::text), 'N/A'::text) AS vehicle_tonnage,
          CASE
              WHEN k.vehicle_gbo THEN 'С ГБО'::text
              ELSE 'Без ГБО'::text
              END AS vehicle_gbo,
          COALESCE(k.vehicle_license, 'N/A'::character varying) AS vehicle_license,
          COALESCE(date_part('year'::text, k.vehicle_date)::text, 'N/A'::text) AS date_year,
          COALESCE(to_char(k.vehicle_date::timestamp with time zone, '"Кв "Q.YYYY'::text), 'N/A'::text) AS date_quarter,
          COALESCE(to_char(k.vehicle_date::timestamp with time zone, 'MM.YYYY'::text), 'N/A'::text) AS date_month,
          COALESCE(to_char(k.vehicle_date::timestamp with time zone, '"Н "IW.IYYY'::text), 'N/A'::text) AS date_week,
          COALESCE(to_char((k.vehicle_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text), 'N/A'::text) AS date_report_week,
          COALESCE(to_char(k.vehicle_date::timestamp with time zone, 'DD.MM.YYYY'::text), 'N/A'::text) AS date_day,
          COALESCE(k.vehicle_status, '0'::character varying) AS ktg_status_id,
          COALESCE(trs.status, 'Не подано в ТГ'::character varying) AS ktg_status,
          CASE
              WHEN k.vehicle_status::text = '5C'::text THEN k.vehicle_reason
              WHEN k.vehicle_status::text = '5B'::text THEN '-1'::integer
              ELSE '-2'::integer
              END AS ktg_reason_id,
          CASE
              WHEN k.vehicle_status::text = '5C'::text AND k.vehicle_reason = 2 AND (trh.end_date - trh.start_date) >= 7 THEN 'Долгострой'::character varying
              WHEN k.vehicle_status::text = '5C'::text THEN trr.reason
              WHEN k.vehicle_status::text = '5B'::text THEN trs.status
              ELSE 'Не подано в ТГ'::character varying
              END AS ktg_reason,
          COALESCE(k.rc_submit_id, 'Не указано'::character varying) AS rc_submit_code,
          COALESCE(k.rc_submit_name, 'Не указано'::character varying) AS rc_submit_name,
          CASE
              WHEN k.ktg THEN 1
              ELSE 0
              END AS ktg_counter,
          CASE
              WHEN k.rg THEN 1
              ELSE 0
              END AS rg_counter
   FROM ts.kip k
            LEFT JOIN ts.organizational_units_timeline o ON k.vehicle_date >= o.start_date AND k.vehicle_date < o.end_date AND k.mvz_id::text = o.mvz_id::text
            LEFT JOIN ts.ts_ready_statuses trs ON k.vehicle_status::text = trs.id::text
            LEFT JOIN ts.ts_ready_reasons trr ON k.vehicle_reason = trr.id
            LEFT JOIN ts.ts_ready_hist trh ON k.vehicle_date >= trh.start_date AND k.vehicle_date < trh.end_date AND k.equnr = trh.equnr;

CREATE OR REPLACE VIEW ts.chart_repair_stats_joined_view
AS SELECT o.mr_id,
          COALESCE(o.mr_name, 'N/A'::character varying) AS mr_name,
          o.territory_id,
          COALESCE(o.territory_name, 'N/A'::character varying) AS territory_name,
          o.atp_id,
          COALESCE(o.atp_name, 'N/A'::character varying) AS atp_name,
          COALESCE(o.atp_type, 'N/A'::character varying) AS atp_type,
          COALESCE(o.mvz_id, 'N/A'::character varying) AS mvz_id,
          COALESCE(o.mvz_name, 'N/A'::character varying) AS mvz_name,
          COALESCE(o.mvz_type, 'N/A'::character varying) AS mvz_type,
          COALESCE(o.retail_network, 'N/A'::character varying) AS retail_network,
          o.repshop_id,
          COALESCE(o.repshop_name, 'N/A'::character varying) AS repshop_name,
          r.vehicle_date,
          COALESCE(r.ts_license_num, 'N/A'::character varying) AS ts_license_num,
          COALESCE(r.ts_marka, 'N/A'::character varying) AS ts_marka,
          COALESCE(r.ts_model, 'N/A'::character varying) AS ts_model,
          COALESCE(r.ts_type, 'N/A'::character varying) AS ts_type,
          COALESCE(r.ts_group, 'N/A'::character varying) AS ts_group,
          CASE
              WHEN r.ts_gbo THEN 'С ГБО'::text
              ELSE 'Без ГБО'::text
              END AS vehicle_gbo,
          COALESCE(to_char(r.ts_load_wgt, 'FM90D0'::text), 'N/A'::text) AS ts_load_wgt,
          date_part('year'::text, r.ts_create_date)::text AS ts_create_year,
          r.mileage,
          COALESCE(date_part('year'::text, r.vehicle_date)::text, 'N/A'::text) AS vehicle_date_year,
          COALESCE(to_char(r.vehicle_date::timestamp with time zone, '"Кв "Q.YYYY'::text), 'N/A'::text) AS quarter_year,
          COALESCE(to_char(r.vehicle_date::timestamp with time zone, 'MM.YYYY'::text), 'N/A'::text) AS month_year,
          COALESCE(to_char(r.vehicle_date::timestamp with time zone, '"Н "IW.IYYY'::text), 'N/A'::text) AS week_year,
          COALESCE(to_char((r.vehicle_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text), 'N/A'::text) AS report_week_year,
          COALESCE(to_char(r.vehicle_date::timestamp with time zone, 'DD.MM.YYYY'::text), 'N/A'::text) AS day_month_year
   FROM ts.repair_stats r
            LEFT JOIN ts.organizational_units_timeline o ON r.mvz_id::text = o.mvz_id::text AND r.vehicle_date >= o.start_date AND r.vehicle_date < o.end_date;

CREATE OR REPLACE VIEW ts.hr_mechanic_org_unit_view
AS SELECT GREATEST(h.from_dttm, ou.start_date) AS start_date,
          LEAST(h.to_dttm, ou.end_date) AS end_date,
          ou.territory_name,
          ou.territory_id,
          ou.mr_name,
          ou.mr_id,
          ou.repshop_name,
          ou.repshop_id,
          ou.atp_name,
          ou.atp_id,
          ou.mvz_name,
          ou.mvz_id,
          h.personnel_no,
          h.emp_status_dk,
          h.rate
   FROM ts.hr_mechanic_full h
            JOIN ts.organizational_units_timeline ou ON h.mvz_id::text = ou.mvz_id::text AND GREATEST(h.from_dttm, ou.start_date) < LEAST(h.to_dttm, ou.end_date);

CREATE OR REPLACE VIEW ts.ktg_detailed_export_view
AS SELECT kip.vehicle_date,
          kip.mvz_id,
          kip.mvz_name,
          kip.vehicle_type,
          kip.vehicle_group,
          kip.vehicle_brand,
          kip.vehicle_model,
          kip.vehicle_tonnage,
          kip.vehicle_license,
          kip.equnr,
          kip.vehicle_vin,
          kip.rc_submit_id,
          kip.rc_submit_name,
          kip.trailer_license,
          organizational_units_timeline.retail_network,
          organizational_units_timeline.atp_type,
          organizational_units_timeline.mr_name,
          organizational_units_timeline.mr_id,
          organizational_units_timeline.atp_id,
          organizational_units_timeline.atp_name,
          organizational_units_timeline.mvz_type,
          ts_data.ts_type,
          ts_data.marka,
          ts_data.model,
          ts_data.load_wgt,
          ts_data.equnr AS ts_data_equnr,
          ts_data.fleet_num,
          types.ts_group,
          COALESCE(ts_ready_statuses.status, 'Не подано в ТГ'::character varying) AS status,
          CASE
              WHEN kip.vehicle_status::text = '5C'::text THEN ts_ready_reasons.reason
              WHEN kip.vehicle_status::text = '5B'::text THEN ts_ready_statuses.status
              ELSE 'Не подано в ТГ'::character varying
              END AS reason,
          CASE
              WHEN kip.ktg = true THEN 1
              ELSE 0
              END AS ktg,
          CASE
              WHEN kip.rg = true THEN 1
              ELSE 0
              END AS rg,
          date_part('year'::text, ts_data.create_date)::integer AS create_date_year,
          organizational_units_timeline.territory_id,
          organizational_units_timeline.territory_name,
          organizational_units_timeline.repshop_name,
          CASE
              WHEN kip.vehicle_gbo THEN 'С ГБО'::text
              ELSE 'Без ГБО'::text
              END AS vehicle_gbo,
          date_part('year'::text, kip.vehicle_date)::integer AS date_year,
          COALESCE(to_char(kip.vehicle_date::timestamp with time zone, '"Кв "Q.YYYY'::text), 'N/A'::character varying::text) AS date_quarter,
          COALESCE(to_char(kip.vehicle_date::timestamp with time zone, 'MM.YYYY'::text), 'N/A'::character varying::text) AS date_month,
          COALESCE(to_char(kip.vehicle_date::timestamp with time zone, '"Н "IW.IYYY'::text), 'N/A'::character varying::text) AS date_week,
          COALESCE(to_char((kip.vehicle_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text), 'N/A'::character varying::text) AS date_report_week,
          COALESCE(to_char(kip.vehicle_date::timestamp with time zone, 'DD.MM.YYYY'::text), 'N/A'::character varying::text) AS date_day
   FROM ts.kip
            LEFT JOIN ts.organizational_units_timeline ON kip.vehicle_date >= organizational_units_timeline.start_date AND kip.vehicle_date < organizational_units_timeline.end_date AND kip.mvz_id::text = organizational_units_timeline.mvz_id::text
            LEFT JOIN ts.ts_data ON kip.trailer_license::text = ts_data.license_num::text
            LEFT JOIN ts.types ON ts_data.ts_type::text = types.ts_type::text
            LEFT JOIN ts.ts_ready_statuses ON kip.vehicle_status::text = ts_ready_statuses.id::text
            LEFT JOIN ts.ts_ready_reasons ON kip.vehicle_reason = ts_ready_reasons.id;

CREATE OR REPLACE VIEW ts.nh_timeline_with_regions
AS WITH dict AS (
    SELECT nh_plan.vehicle_id,
           nh_plan.plan_nh,
           nh_plan.start_date,
           lead(nh_plan.start_date, 1, '9999-12-31'::date) OVER (PARTITION BY nh_plan.vehicle_id ORDER BY nh_plan.start_date) AS end_date
    FROM ts.nh_plan
)
   SELECT m.one_day,
          m.equnr,
          ou.repshop_id,
          ou.repshop_name,
          ou.atp_id,
          ou.atp_name,
          ou.mr_id,
          ou.mr_name,
          m.mvz_id,
          ou.mvz_name,
          ou.territory_name,
          ou.territory_id,
          COALESCE(d.plan_nh, 0::double precision) / date_part('days'::text, date_trunc('month'::text, m.one_day::timestamp with time zone) + '1 mon'::interval - '1 day'::interval) AS nh_daily_value
   FROM ts.monitoring m
            JOIN ts.organizational_units_timeline ou ON m.mvz_id::text = ou.mvz_id::text AND m.one_day >= ou.start_date AND m.one_day < ou.end_date
            JOIN ts.ts_data td ON td.equnr = m.equnr
            JOIN ts.nh_vehicles v ON v.type::text = td.ts_type::text AND v.brand::text = td.marka::text AND v.vehicle_create_year::double precision = date_part('YEAR'::text, td.create_date) AND v.vehicle_tonnage = td.load_wgt
            LEFT JOIN dict d ON v.id = d.vehicle_id AND m.one_day >= d.start_date AND m.one_day < d.end_date
   WHERE (m.mvz_type::text = ANY (ARRAY['Основной'::character varying::text, 'КДК'::character varying::text])) AND (m.ts_group::text = ANY (ARRAY['Основное'::character varying::text, 'Полуприцеп'::character varying::text, 'Прицеп'::character varying::text]));

CREATE OR REPLACE VIEW ts.repair_detailed_export
AS SELECT r.order_id,
          o.territory_id AS vehicle_territory_id,
          o.territory_name AS vehicle_territory_name,
          o.mr_id AS vehicle_mr_id,
          o.mr_name AS vehicle_mr,
          o.atp_id AS vehicle_atp_id,
          o.atp_name AS vehicle_atp,
          r.final_repshop_name,
          o.mvz_id AS vehicle_mvz,
          o.mvz_name AS vehicle_mvz_name,
          o.retail_network AS vehicle_retail_network,
          o.atp_type AS vehicle_atp_type,
          o.mvz_type AS vehicle_mvz_type,
          r.order_sys_stat,
          r.repair_start_date,
          r.repair_kind,
          r.order_mvz,
          r.mvz_name,
          r.req_id,
          r.req_type,
          r.req_user_stat,
          r.req_sys_stat,
          r.order_text,
          r.repair_type_name,
          r.repair_subtype_name,
          r.vrt,
          r.vrt_name,
          r.event_id,
          r.event_text,
          r.equnr,
          r.ts_group,
          r.ts_type,
          r.ts_marka,
          r.ts_model,
          r.ts_create_date,
          r.ts_load_wgt,
          r.ts_gbo,
          r.payout_date,
          r.repair_end_date,
          r.repair_plan_end_date,
          r.deblock_date,
          r.services_amount,
          r.services_expenses,
          r.repair_expenses,
          to_char(r.repair_start_date::timestamp with time zone, 'DD.MM.YYYY'::text) AS day,
          to_char(r.repair_start_date::timestamp with time zone, '"Н "IW.IYYY'::text) AS week,
          to_char((r.repair_start_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text) AS work_week,
          to_char(r.repair_start_date::timestamp with time zone, 'MM.YYYY'::text) AS month,
          to_char(r.repair_start_date::timestamp with time zone, '"Кв "Q.YYYY'::text) AS quarter,
          to_char(r.repair_start_date::timestamp with time zone, 'YYYY'::text) AS year
   FROM ts.repair r
            LEFT JOIN ts.organizational_units_timeline o ON r.vehicle_mvz_id::text = o.mvz_id::text AND r.repair_start_date >= o.start_date AND r.repair_start_date < o.end_date;

CREATE OR REPLACE VIEW ts.repair_detailed_view
AS SELECT r.order_id::character varying AS order_id,
          o.mr_id AS vehicle_mr_id,
          o.mr_name AS vehicle_mr,
          o.atp_id AS vehicle_atp_id,
          o.atp_name AS vehicle_atp,
          r.vehicle_mvz,
          o.territory_id,
          o.territory_name,
          o.mvz_id AS vehicle_mvz_id,
          o.mvz_name AS vehicle_mvz_name,
          o.retail_network AS vehicle_retail_network,
          o.atp_type AS vehicle_atp_type,
          o.mvz_type AS vehicle_mvz_type,
          r.final_repshop_name AS repair_atp,
          r.order_sys_stat,
          r.repair_kind,
          r.creditor_number::character varying AS creditor_number,
          r.creditor_name,
          r.req_id,
          r.order_text,
          twt.name AS repair_type_name,
          tws.name AS repair_subtype_name,
          r.vrt,
          tw.name AS vrt_name,
          r.event_id,
          r.event_text,
          r.ts_group,
          r.ts_type,
          r.ts_marka,
          r.ts_model,
          r.vehicle_year,
          date_part('year'::text, r.ts_create_date) AS ts_create_date,
          r.ts_load_wgt,
          r.ts_gbo,
          r.ts_license_num,
          r.equnr::character varying AS equnr,
          r.vin,
          r.mechanic_id,
          r.repair_start_date,
          r.repair_end_date,
          r.deblock_date,
          r.invoice_date,
          r.invoice_number,
          r.payout_date,
          r.mileage,
          r.repair_expenses_plan,
          r.repair_expenses_fact,
          r.services_expenses,
          r.repair_expenses_full,
          r.part_expenses,
          r.our_workshop,
          CASE
              WHEN r.our_workshop = true THEN COALESCE(r.services_expenses, 0::double precision)
              ELSE 0::double precision
              END AS repair_services_cost_our_work,
          CASE
              WHEN r.our_workshop = false THEN COALESCE(r.services_expenses, 0::double precision)
              ELSE 0::double precision
              END AS repair_services_cost_ext_work,
          r.services_amount
   FROM ts.repair r
            LEFT JOIN ts.organizational_units_timeline o ON r.vehicle_mvz_id::text = o.mvz_id::text AND o.start_date <= r.repair_start_date AND r.repair_start_date < o.end_date
            LEFT JOIN ts.toro_works tw ON r.vrt::text = tw.id::text
            LEFT JOIN ts.toro_works_types twt ON tw.type_id = twt.id
            LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;

CREATE OR REPLACE VIEW ts.repair_expenses_by_order_and_vehicle
AS SELECT r.order_id,
          r.equnr,
          r.repair_start_date,
          org.mr_id,
          org.mr_name,
          org.atp_id,
          org.atp_name,
          org.atp_type,
          org.mvz_id,
          org.mvz_name,
          org.mvz_type,
          org.retail_network,
          org.territory_id,
          org.territory_name,
          r.our_workshop,
          r.ts_marka,
          r.ts_load_wgt,
          r.ts_type,
          r.ts_group,
          r.vrt AS vrt_id,
          tw.name AS vrt_name,
          tws.id AS subtype_id,
          COALESCE(tws.name, 'Не указано'::character varying) AS subtype_name,
          r.repair_expenses_full,
          r.services_expenses,
          CASE
              WHEN r.our_workshop AND (r.vrt::text <> ALL (ARRAY['S1'::character varying::text, 'N1'::character varying::text])) THEN r.services_expenses
              ELSE 0::double precision
              END AS our_workshop_services_expenses,
          CASE
              WHEN NOT r.our_workshop AND (r.vrt::text <> ALL (ARRAY['S1'::character varying::text, 'N1'::character varying::text])) THEN r.services_expenses
              ELSE 0::double precision
              END AS ext_workshop_services_expenses,
          CASE
              WHEN r.vrt::text = ANY (ARRAY['S1'::character varying::text, 'N1'::character varying::text]) THEN r.services_expenses
              ELSE 0::double precision
              END AS tires_services_expenses,
          r.tires_expenses AS tires,
          r.tires_parts_expenses AS tires_parts,
          r.part_expenses AS parts,
          rrkp.coef
   FROM ts.repair r
            JOIN ts.organizational_units_timeline org ON r.vehicle_mvz_id::text = org.mvz_id::text AND org.start_date <= r.repair_start_date AND r.repair_start_date < org.end_date
            LEFT JOIN ts.toro_works tw ON r.vrt::text = tw.id::text
            LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id
            LEFT JOIN ts.repairs_rub_km_plan rrkp ON rrkp.date_from <= r.repair_start_date AND r.repair_start_date < rrkp.date_to;

CREATE OR REPLACE VIEW ts.repair_org_unit_timeline_joined_view
AS SELECT repair.repair_start_date,
          repair.order_id,
          repair.repair_expenses_full,
          repair.order_sys_stat,
          repair.order_ut,
          repair.order_mvz,
          repair.vehicle_mvz,
          repair.vehicle_repshop_id,
          repair.vehicle_repshop_name,
          repair.order_repshop_id,
          repair.order_repshop_name,
          repair.req_id,
          repair.req_type,
          repair.req_user_stat,
          repair.req_sys_stat,
          repair.repair_kind,
          repair.order_text,
          repair.our_workshop,
          repair.vrt,
          repair.vrt_name,
          repair.event_id,
          repair.event_text,
          repair.equnr,
          repair.vehicle_bu,
          repair.main_mechanic_id,
          repair.main_mechanic_mvz_id,
          repair.main_mechanic_mvz_name,
          repair.main_mechanic_mvz_type,
          repair.main_mechanic_atp_id,
          repair.main_mechanic_atp_name,
          repair.main_mechanic_repshop_id,
          repair.main_mechanic_repshop_name,
          repair.invoice_number,
          repair.creditor_number,
          repair.creditor_name,
          repair.resources_creditor_number,
          repair.services_creditor_number,
          repair.mileage,
          repair.repair_expenses,
          repair.repair_expenses_plan,
          repair.repair_expenses_fact,
          repair.invoice_date,
          repair.payout_date,
          repair.repair_end_date,
          repair.repair_plan_end_date,
          repair.deblock_date,
          repair.repair_subtype_id,
          repair.repair_subtype_name,
          repair.repair_subtype_color,
          repair.repair_type_id,
          repair.repair_type_name,
          repair.ts_license_num,
          repair.ts_marka,
          repair.ts_model,
          repair.ts_type,
          repair.ts_group,
          repair.ts_gbo,
          repair.ts_load_wgt,
          repair.ts_create_date,
          repair.vehicle_year,
          repair.vin,
          repair.part_amount,
          repair.part_expenses,
          repair.services_amount,
          repair.services_expenses,
          repair.services_avg_price,
          repair.mechanic_id,
          repair.services_atp_id,
          repair.services_atp_name,
          repair.services_repshop_id,
          repair.services_repshop_name,
          repair.services_mvz_id,
          repair.final_service_mvz_id,
          repair.final_repshop_id,
          repair.final_repshop_name,
          repair.unique_services_repshop_flag,
          repair.unique_mechanic_id_flag,
          ou_timeline.mvz_id,
          ou_timeline.mvz_name,
          ou_timeline.mvz_type,
          ou_timeline.atp_id,
          ou_timeline.atp_name,
          ou_timeline.atp_type,
          ou_timeline.retail_network,
          ou_timeline.mr_id,
          ou_timeline.mr_name,
          ou_timeline.repshop_id,
          ou_timeline.repshop_name,
          ou_timeline.hr_place_id,
          ou_timeline.hr_place_name,
          ou_timeline.hr_tplace_id,
          ou_timeline.hr_tplace_name
   FROM ts.repair
            JOIN ts.organizational_units_timeline ou_timeline ON ou_timeline.mvz_id::text = repair.vehicle_mvz_id::text AND repair.repair_start_date >= ou_timeline.start_date AND repair.repair_start_date < ou_timeline.end_date;

CREATE OR REPLACE VIEW ts.repair_rates_view
AS WITH rr_dictionary_main AS (
    SELECT rr_dictionary_1_1.atp_id,
           rr_dictionary_1_1.toro_work_id,
           rr_dictionary_1_1.tonnage,
           rr_dictionary_1_1.start_date,
           rr_dictionary_1_1.end_date,
           structures_1.structure_name,
           rr_dictionary_1_1.rate * (COALESCE(rsd.rate, 0::numeric) / 100::numeric)::double precision AS rate
    FROM ts.rr_dictionary rr_dictionary_1_1
             CROSS JOIN ( VALUES ('ourWorkshopServices'::text), ('extWorkshopServices'::text), ('parts'::text), ('tires'::text)) structures_1(structure_name)
             LEFT JOIN ts.rr_structure_dictionary rsd ON rsd.year = rr_dictionary_1_1.year AND rsd.month = rr_dictionary_1_1.month AND rsd.atp_id = rr_dictionary_1_1.atp_id AND rsd.structure_type::text = structures_1.structure_name
)
   SELECT rr_dictionary_1.start_date,
          rr_dictionary_1.end_date,
          rr_dictionary_1.atp_id,
          rr_dictionary_1.toro_work_id,
          COALESCE(tw.name, 'N/A'::character varying) AS toro_works,
          COALESCE(twt.name, 'N/A'::character varying) AS toro_type_name,
          COALESCE(tws.name, 'N/A'::character varying) AS toro_subtype_name,
          rr_dictionary_1.tonnage,
          CASE
              WHEN rr_dictionary_1.structure_name = 'ourWorkshopServices'::text THEN 'Ремзона'::text
              WHEN rr_dictionary_1.structure_name = 'extWorkshopServices'::text THEN 'СТО'::text
              WHEN rr_dictionary_1.structure_name = 'parts'::text THEN 'Запчасти'::text
              WHEN rr_dictionary_1.structure_name = 'tires'::text THEN 'Шины'::text
              ELSE NULL::text
              END AS structure_name,
          rr_dictionary_1.rate
   FROM rr_dictionary_main rr_dictionary_1
            LEFT JOIN ts.toro_works tw ON rr_dictionary_1.toro_work_id::text = tw.id::text
            LEFT JOIN ts.toro_works_types twt ON tw.subtype_id = twt.id
            LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;

CREATE OR REPLACE VIEW ts.repair_resources_view
AS SELECT rr.order_id,
          rr.repair_start_date,
          rr.equnr,
          o.territory_id AS vehicle_territory_id,
          o.territory_name AS vehicle_territory_name,
          o.mvz_id AS vehicle_mvz_id,
          o.mvz_name AS vehicle_mvz_name,
          o.mvz_type AS vehicle_mvz_type,
          o.atp_id AS vehicle_atp_id,
          o.atp_name AS vehicle_atp,
          o.atp_type AS vehicle_atp_type,
          o.retail_network AS vehicle_retail_network,
          o.mr_id AS vehicle_mr_id,
          o.mr_name AS vehicle_mr,
          rr.vrt,
          tw.name AS vrt_name,
          tws.id AS repair_subtype_id,
          tws.name AS repair_subtype_name,
          twt.id AS repair_type_id,
          twt.name AS repair_type_name,
          rr.ts_load_wgt,
          rr.ts_marka,
          rr.ts_model,
          rr.ts_create_date,
          rr.ts_license_num,
          rr.material_type_id,
          rr.material_type_name,
          rr.material_group_id,
          rr.material_group_name,
          rr.material_parent_id,
          rr.material_parent_name,
          rr.material_article,
          rr.material_produce,
          rr.ts_type,
          rr.ts_group,
          rr.ts_gbo,
          rr.material_id,
          rr.material_name,
          rr.gl_account,
          rr.material_amount_plan,
          rr.material_amount_fact,
          rr.material_amount,
          rr.material_expenses,
          rr.material_price,
          date_part('year'::text, rr.ts_create_date) AS vehicle_year,
          rr.repair_kind
   FROM ts.repair_resources rr
            LEFT JOIN ts.organizational_units_timeline o ON rr.vehicle_mvz_id::text = o.mvz_id::text AND o.start_date <= rr.repair_start_date AND rr.repair_start_date < o.end_date
            LEFT JOIN ts.toro_works tw ON rr.vrt::text = tw.id::text
            LEFT JOIN ts.toro_works_types twt ON tw.type_id = twt.id
            LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;

CREATE OR REPLACE VIEW ts.repair_services_view
AS SELECT oum.mr_id AS mechanic_mr_id,
          oum.mr_name AS mechanic_mr_name,
          oum.territory_name AS ter_name,
          oum.repshop_id AS mechanic_repshop_id,
          oum.repshop_name AS mechanic_repshop_name,
          oum.atp_id AS mechanic_atp_repair_id,
          oum.atp_name AS mechanic_atp_repair_name,
          ouv.mr_id AS vehicle_mr_id,
          ouv.mr_name AS vehicle_mr,
          ouv.territory_name AS service_ter_name,
          ouv.atp_id AS vehicle_atp_id,
          ouv.atp_name AS vehicle_atp,
          ouv.mvz_id AS vehicle_mvz_id,
          ouv.mvz_name AS vehicle_mvz_name,
          ouv.repshop_id AS vehicle_repshop_id,
          ouv.repshop_name AS vehicle_repshop_name,
          rs.equnr,
          rs.repair_start_date,
          rs.service_amount,
          rs.repair_kind,
          rs.mechanic_id,
          rs.our_workshop,
          oum.territory_id,
          ouv.territory_id AS service_territory_id
   FROM ts.repair_services rs
            LEFT JOIN ts.organizational_units_timeline oum ON oum.start_date <= rs.repair_start_date AND rs.repair_start_date < oum.end_date AND rs.mechanic_mvz_id::text = oum.mvz_id::text
            LEFT JOIN ts.organizational_units_timeline ouv ON ouv.start_date <= rs.repair_start_date AND rs.repair_start_date < ouv.end_date AND rs.vehicle_mvz_id::text = ouv.mvz_id::text;


CREATE OR REPLACE VIEW ts.repair_services_with_rephops_view
AS SELECT row_number() OVER () AS ctid,
          rs.order_id,
          rs.service_id,
          rs.service_name,
          rs.service_amount,
          rs.service_expenses,
          rs.creditor_number,
          rs.creditor_name,
          rs.purchase_order_id,
          rs.mechanic_id,
          rs.service_row_num,
          rs.service_expenses_kind,
          rs.vrt,
          tw.use_nh,
          rs.our_workshop,
          rs.repair_start_date,
          rs.vehicle_mr_id,
          rs.vehicle_atp_id,
          rs.vehicle_mvz_id,
          rs.vehicle_retail_network,
          rs.vehicle_atp_type,
          rs.vehicle_mvz_type,
          vtl.repshop_id AS vehicle_rephop_id,
          mtl.repshop_id AS mechanic_rephop_id
   FROM ts.repair_services rs
            LEFT JOIN ( SELECT organizational_units_timeline.start_date,
                               organizational_units_timeline.end_date,
                               organizational_units_timeline.mvz_id,
                               organizational_units_timeline.repshop_id,
                               organizational_units_timeline.repshop_name
                        FROM ts.organizational_units_timeline) vtl ON rs.vehicle_mvz_id::text = vtl.mvz_id::text AND vtl.start_date <= rs.repair_start_date AND rs.repair_start_date < vtl.end_date
            LEFT JOIN ( SELECT organizational_units_timeline.start_date,
                               organizational_units_timeline.end_date,
                               organizational_units_timeline.mvz_id,
                               organizational_units_timeline.repshop_id,
                               organizational_units_timeline.repshop_name
                        FROM ts.organizational_units_timeline) mtl ON rs.mechanic_mvz_id::text = mtl.mvz_id::text AND mtl.start_date <= rs.repair_start_date AND rs.repair_start_date < mtl.end_date
            LEFT JOIN ts.toro_works tw ON tw.id::text = rs.vrt::text;

CREATE OR REPLACE VIEW ts.repair_stats_org_ut_view
AS SELECT org.mr_id,
          org.mr_name,
          org.atp_id,
          org.atp_name,
          org.atp_type,
          org.mvz_id,
          org.mvz_name,
          org.mvz_type,
          org.retail_network,
          rs.vehicle_date,
          rs.ts_marka,
          rs.ts_load_wgt,
          rs.ts_type,
          rs.ts_group,
          rs.mileage,
          rs.mileage_trailer,
          rs.mileage_full
   FROM ts.repair_stats rs
            JOIN ts.organizational_units_timeline org ON rs.mvz_id::text = org.mvz_id::text AND org.start_date <= rs.vehicle_date AND rs.vehicle_date < org.end_date;

CREATE OR REPLACE VIEW ts.repshop_properties_timeline
AS WITH days AS (
    SELECT generate_series((( SELECT organizational_units_timeline.start_date
                              FROM ts.organizational_units_timeline
                              ORDER BY organizational_units_timeline.start_date
                              LIMIT 1))::timestamp without time zone::timestamp with time zone, now() + '3 years'::interval, '1 day'::interval)::date AS day
), dict_day_posts AS (
    SELECT rp.rs_id,
           rp.year,
           date_part('month'::text, to_date(rp.month::text, 'Month'::text)) AS month_number,
           rp.value AS day_posts
    FROM ts.repshops_properties rp
    WHERE rp.property::text = 'day_posts'::text
), dict_night_posts AS (
    SELECT rp.rs_id,
           rp.year,
           date_part('month'::text, to_date(rp.month::text, 'Month'::text)) AS month_number,
           rp.value AS night_posts
    FROM ts.repshops_properties rp
    WHERE rp.property::text = 'night_posts'::text
), dict_total_posts AS (
    SELECT rp.rs_id,
           rp.year,
           date_part('month'::text, to_date(rp.month::text, 'Month'::text)) AS month_number,
           rp.value AS total_posts
    FROM ts.repshops_properties rp
    WHERE rp.property::text = 'total_posts'::text
), dict_posts_performance AS (
    SELECT rp.rs_id,
           rp.year,
           date_part('month'::text, to_date(rp.month::text, 'Month'::text)) AS month_number,
           rp.value AS post_performance
    FROM ts.repshops_properties rp
    WHERE rp.property::text = 'post_performance'::text
), dict_mech_performance AS (
    SELECT rp.rs_id,
           rp.year,
           date_part('month'::text, to_date(rp.month::text, 'Month'::text)) AS month_number,
           rp.value AS mech_performance
    FROM ts.repshops_properties rp
    WHERE rp.property::text = 'mech_performance'::text
)
   SELECT d.day,
          ut.mr_id,
          ut.mr_name,
          ut.territory_name,
          ut.territory_id,
          ut.repshop_id,
          ut.repshop_name,
          ut.atp_id,
          ut.atp_name,
          ut.atp_type,
          ut.mvz_id,
          ut.mvz_name,
          ut.mvz_type,
          ut.retail_network,
          dp.day_posts,
          np.night_posts,
          tp.total_posts,
          pp.post_performance::double precision / date_part('days'::text, date_trunc('month'::text, d.day::timestamp with time zone) + '1 mon -1 days'::interval) AS daily_performance,
          mp.mech_performance::double precision / date_part('days'::text, date_trunc('month'::text, d.day::timestamp with time zone) + '1 mon -1 days'::interval) AS daily_mech_performance
   FROM days d
            LEFT JOIN ts.organizational_units_timeline ut ON ut.start_date <= d.day AND d.day < ut.end_date
            LEFT JOIN dict_day_posts dp ON date_part('year'::text, d.day) = dp.year::double precision AND date_part('month'::text, d.day) = dp.month_number AND ut.repshop_id = dp.rs_id
            LEFT JOIN dict_night_posts np ON date_part('year'::text, d.day) = np.year::double precision AND date_part('month'::text, d.day) = np.month_number AND ut.repshop_id = np.rs_id
            LEFT JOIN dict_total_posts tp ON date_part('year'::text, d.day) = tp.year::double precision AND date_part('month'::text, d.day) = tp.month_number AND ut.repshop_id = tp.rs_id
            LEFT JOIN dict_posts_performance pp ON date_part('year'::text, d.day) = pp.year::double precision AND date_part('month'::text, d.day) = pp.month_number AND ut.repshop_id = pp.rs_id
            LEFT JOIN dict_mech_performance mp ON date_part('year'::text, d.day) = mp.year::double precision AND date_part('month'::text, d.day) = mp.month_number AND ut.repshop_id = mp.rs_id;


CREATE OR REPLACE VIEW ts.vehicle_daily_timeline
AS WITH days AS (
    SELECT generate_series('2018-01-01'::date::timestamp with time zone, now() + '3 years'::interval, '1 day'::interval)::date AS day
)
   SELECT vrt.equnr,
          td.create_date,
          days.day,
          org.mvz_id,
          org.atp_id,
          org.mr_id,
          org.mvz_type,
          org.atp_type,
          org.retail_network,
          org.territory_id,
          org.territory_name,
          vrt.is_inactive,
          t.ts_group
   FROM days
            JOIN ts.vehicle_region_timeline vrt ON vrt.start_date <= days.day AND days.day < vrt.end_date
            JOIN ts.organizational_units_timeline org ON vrt.mvz_id::text = org.mvz_id::text AND org.start_date <= days.day AND days.day < org.end_date
            JOIN ts.ts_data td ON vrt.equnr = td.equnr
            JOIN ts.types t ON td.ts_type::text = t.ts_type::text;


CREATE OR REPLACE VIEW ts.vehicle_org_units_timeline
AS SELECT GREATEST(ou_timeline.start_date, vrt.start_date) AS start_date,
          LEAST(ou_timeline.end_date, vrt.end_date) AS end_date,
          vrt.equnr,
          vrt.mvz_id,
          ou_timeline.mvz_name,
          ou_timeline.mvz_type,
          ou_timeline.atp_id,
          ou_timeline.atp_name,
          ou_timeline.atp_type,
          ou_timeline.retail_network,
          ou_timeline.mr_id,
          ou_timeline.mr_name,
          ou_timeline.repshop_id,
          ou_timeline.repshop_name,
          ou_timeline.hr_place_id,
          ou_timeline.hr_place_name,
          ou_timeline.hr_tplace_id,
          ou_timeline.hr_tplace_name,
          ou_timeline.territory_id,
          ou_timeline.territory_name,
          vrt.is_inactive
   FROM ts.vehicle_region_timeline vrt
            JOIN ts.organizational_units_timeline ou_timeline ON ou_timeline.mvz_id::text = vrt.mvz_id::text
   WHERE GREATEST(ou_timeline.start_date, vrt.start_date) < LEAST(ou_timeline.end_date, vrt.end_date) AND NOT vrt.is_inactive;

CREATE OR REPLACE VIEW ts.vehicles_full_view
AS SELECT ou.mvz_id,
          ou.mvz_name,
          ou.mvz_type,
          ou.atp_id,
          ou.atp_name,
          ou.retail_network,
          ou.atp_type,
          ou.mr_id,
          ou.mr_name,
          ou.repshop_id,
          ou.repshop_name,
          ou.hr_place_id,
          ou.hr_place_name,
          ou.hr_tplace_id,
          ou.hr_tplace_name,
          ou.territory_id,
          ou.territory_name,
          vrt.equnr,
          vrt.is_inactive,
          td.ts_type,
          types.ts_group,
          td.create_date,
          td.load_wgt,
          td.marka,
          GREATEST(vrt.start_date, ou.start_date) AS start_date,
          LEAST(vrt.end_date, ou.end_date) AS end_date
   FROM ts.vehicle_region_timeline vrt
            JOIN ts.ts_data td ON td.equnr = vrt.equnr
            JOIN ts.types ON td.ts_type::text = types.ts_type::text
            JOIN ts.organizational_units_timeline ou ON vrt.mvz_id::text = ou.mvz_id::text AND GREATEST(vrt.start_date, ou.start_date) < LEAST(vrt.end_date, ou.end_date);


CREATE MATERIALIZED view if not EXISTS ts.pls_by_day_with_rate
    TABLESPACE pg_default
AS WITH mvz_log AS (
    SELECT mvz_log_1.uid AS mvz_id,
           mvz_log_1.atp_id,
           mvz_log_1.start_date,
           lead(mvz_log_1.start_date, 1) OVER (PARTITION BY mvz_log_1.uid ORDER BY mvz_log_1.start_date) AS end_date
    FROM ts.mvz_log mvz_log_1
)
   SELECT pbd.qmnum,
          pbd.vehicle_date,
          pbd.mvz_id,
          pbd.mvz_name,
          pbd.vehicle_license,
          pbd.marka,
          pbd.model,
          pbd.ts_type,
          pbd.ts_group,
          pbd.gbo,
          pbd.gbo_text,
          pbd.load_wgt,
          pbd.equnr,
          pbd.year,
          pbd.create_date,
          pbd.fleet_num,
          pbd.no_compart,
          pbd.pl_tonnage,
          pbd.bequi,
          pbd.trailer_license_num,
          pbd.trailer_load_wgt,
          pbd.start_timestamp,
          pbd.end_timestamp,
          pbd.usage_time,
          pbd.avg_speed,
          pbd.mileage,
          pbd.model_flag,
          mvz_log.atp_id,
          rrv.toro_work_id,
          rrv.toro_works,
          rrv.toro_type_name,
          rrv.toro_subtype_name,
          rrv.structure_name,
          rrv.rate
   FROM ts.pls_by_day pbd
            LEFT JOIN mvz_log ON pbd.mvz_id::text = mvz_log.mvz_id::text AND pbd.vehicle_date >= mvz_log.start_date AND pbd.vehicle_date < mvz_log.end_date
            LEFT JOIN ts.repair_rates_view rrv ON mvz_log.atp_id = rrv.atp_id AND pbd.vehicle_date >= rrv.start_date AND pbd.vehicle_date < rrv.end_date AND pbd.pl_tonnage = rrv.tonnage
WITH DATA;

CREATE INDEX  IF NOT exists pls_by_day_with_rate_idx ON ts.pls_by_day_with_rate USING btree (mvz_id, vehicle_date, pl_tonnage);
