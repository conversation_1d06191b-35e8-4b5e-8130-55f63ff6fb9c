package com.x5.logistics.repository

import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.dictionary.AtpRepairShopLog
import com.x5.logistics.data.dictionary.RepairShop
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Instant
import java.time.LocalDate

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class RepairShopsDictionaryRepositoryTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var repository: RepairShopsDictionaryRepository

    @Autowired
    private lateinit var db: Database

    @BeforeEach
    fun setUp() {
        transaction(db) {
            SchemaUtils.drop(
                RepairShop,
                AtpRepairShopLog
            )
            SchemaUtils.create(
                RepairShop,
                AtpRepairShopLog
            )

            // Insert test data for RepairShop
            RepairShop.insert {
                it[id] = 1
                it[name] = "Active Repair Shop 1"
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            RepairShop.insert {
                it[id] = 2
                it[name] = "Active Repair Shop 2"
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            RepairShop.insert {
                it[id] = 3
                it[name] = "Deleted Repair Shop"
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = true
            }

            RepairShop.insert {
                it[id] = 4
                it[name] = "Active But Not Referenced Repair Shop"
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            // Insert test data for AtpRepairShopLog
            AtpRepairShopLog.insert {
                it[id] = 1
                it[atpId] = 101
                it[rsId] = 1 // References Active Repair Shop 1
                it[startDate] = LocalDate.now()
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            AtpRepairShopLog.insert {
                it[id] = 2
                it[atpId] = 102
                it[rsId] = 2 // References Active Repair Shop 2
                it[startDate] = LocalDate.now()
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            AtpRepairShopLog.insert {
                it[id] = 3
                it[atpId] = 103
                it[rsId] = 3 // References Deleted Repair Shop
                it[startDate] = LocalDate.now()
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = false
            }

            AtpRepairShopLog.insert {
                it[id] = 4
                it[atpId] = 104
                it[rsId] = 1 // Another reference to Active Repair Shop 1
                it[startDate] = LocalDate.now()
                it[createdAt] = Instant.now()
                it[createdBy] = "Test User"
                it[updatedAt] = Instant.now()
                it[updatedBy] = "Test User"
                it[deleted] = true // This one is deleted
            }
        }
    }

    @Test
    fun `test getActiveRepairShops returns active repair shops`() {
        // Execute the function being tested
        val result = repository.getActiveRepairShops()

        // Verify the results
        // Should contain only Active Repair Shop 1 and Active Repair Shop 2
        assert(result.size == 2) { "Expected 2 active repair shops, but got ${result.size}" }

        // Verify the content of the results
        val shop1 = result.find { it.value == 1 }
        val shop2 = result.find { it.value == 2 }

        assert(shop1 != null) { "Active Repair Shop 1 should be in the results" }
        assert(shop2 != null) { "Active Repair Shop 2 should be in the results" }

        assert(shop1?.label == "Active Repair Shop 1") { "Label for shop 1 should be 'Active Repair Shop 1'" }
        assert(shop2?.label == "Active Repair Shop 2") { "Label for shop 2 should be 'Active Repair Shop 2'" }

        // Verify that deleted repair shop and non-referenced repair shop are not in the results
        assert(result.none { it.value == 3 }) { "Deleted Repair Shop should not be in the results" }
        assert(result.none { it.value == 4 }) { "Non-referenced Repair Shop should not be in the results" }
    }
}
