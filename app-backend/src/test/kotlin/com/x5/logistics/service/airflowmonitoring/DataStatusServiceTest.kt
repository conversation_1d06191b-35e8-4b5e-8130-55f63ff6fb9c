package com.x5.logistics.service.airflowmonitoring

import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.airflowmonitoring.AllTablesInfo
import com.x5.logistics.data.airflowmonitoring.AllTablesUpdateLog
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.LocalDateTime

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class DataStatusServiceTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var service: DataStatusService

    @Autowired
    private lateinit var db: Database

    @BeforeEach
    fun setUp() {
        transaction(db) {
            SchemaUtils.create(
                AllTablesInfo,
                AllTablesUpdateLog
            )
            AllTablesInfo.insert {
                it[fullTableName] = "FULL_TABLE_NAME"
                it[isUpdatedByAirflow] = true
            }

            AllTablesUpdateLog.insert {
                it[logTableName] = "FULL_TABLE_NAME"
                it[updateStartAt] = LocalDateTime.now()
            }
        }
    }

    @Test
    fun `test getDataStatus works`() {
        val resp = runBlocking {
            service.getDataStatus()
        }

        with(resp) {
            assert(currentStatus.isNotEmpty())
            assert(nextUpdateStatus.isNotEmpty())
        }
    }
}