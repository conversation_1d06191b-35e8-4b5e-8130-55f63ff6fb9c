package com.x5.logistics.service.reportinfo

import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.reportinfo.ColumnInfoTable
import com.x5.logistics.data.reportinfo.ReportInfoTable
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.sql.SQLException

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class ReportInfoServiceTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var service: ReportInfoService

    @Autowired
    private lateinit var database: Database

    @BeforeEach
    fun setup() {
        transaction(database) {
            addLogger(StdOutSqlLogger)
            SchemaUtils.drop(ColumnInfoTable, ReportInfoTable)
            SchemaUtils.create(ReportInfoTable, ColumnInfoTable)
        }
    }

    @AfterEach
    fun cleanup() {
        transaction(database) {
            SchemaUtils.drop(ColumnInfoTable, ReportInfoTable)
        }
    }

    @Test
    fun `test getColumns returns correct data`() = runBlocking {
        // Arrange
        transaction(database) {
            val reportId = ReportInfoTable.insert {
                it[report] = "test_report"
                it[reportDescription] = "Test Report Description"
                it[granularityInfo] = "Test Granularity Info"
            } get ReportInfoTable.id

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "column1"
                it[field] = "field1"
                it[content] = "content1"
                it[isDisplayed] = true
                it[order] = 1
            }

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "column2"
                it[field] = "field2"
                it[content] = "content2"
                it[isDisplayed] = true
                it[order] = 2
            }
        }

        // Act
        val result = service.getColumns("test_report")

        // Assert
        with(result) {
            assertEquals("test_report", report)
            assertEquals("Test Report Description", reportDescription)
            assertEquals("Test Granularity Info", granularityInfo)
            assertEquals(2, columns.size)
            
            with(columns[0]) {
                assertEquals("column1", name)
                assertEquals(1, info.size)
                assertEquals("field1", info[0].field)
                assertEquals("content1", info[0].content)
            }
            
            with(columns[1]) {
                assertEquals("column2", name)
                assertEquals(1, info.size)
                assertEquals("field2", info[0].field)
                assertEquals("content2", info[0].content)
            }
        }
    }

    @Test
    fun `test getColumns with non-existent report throws exception`() = runBlocking {
        assertThrows<SQLException> {
            service.getColumns("non_existent_report")
        }
    }

    @Test
    fun `test getColumns returns only displayed columns`() = runBlocking {
        // Arrange
        transaction(database) {
            val reportId = ReportInfoTable.insert {
                it[report] = "test_report"
                it[reportDescription] = "Test Report Description"
                it[granularityInfo] = "Test Granularity Info"
            } get ReportInfoTable.id

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "visible_column"
                it[field] = "field1"
                it[content] = "content1"
                it[isDisplayed] = true
                it[order] = 1
            }

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "hidden_column"
                it[field] = "field2"
                it[content] = "content2"
                it[isDisplayed] = false
                it[order] = 2
            }
        }

        // Act
        val result = service.getColumns("test_report")

        // Assert
        assertEquals(1, result.columns.size)
        assertEquals("visible_column", result.columns[0].name)
    }

    @Test
    fun `test getColumns respects column order`() = runBlocking {
        // Arrange
        transaction(database) {
            val reportId = ReportInfoTable.insert {
                it[report] = "test_report"
                it[reportDescription] = "Test Description"
                it[granularityInfo] = "Test Granularity"
            } get ReportInfoTable.id

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "second_column"
                it[field] = "field2"
                it[content] = "content2"
                it[isDisplayed] = true
                it[order] = 2
            }

            ColumnInfoTable.insert {
                it[report] = reportId
                it[name] = "first_column"
                it[field] = "field1"
                it[content] = "content1"
                it[isDisplayed] = true
                it[order] = 1
            }
        }

        // Act
        val result = service.getColumns("test_report")

        // Assert
        assertEquals(2, result.columns.size)
        assertEquals("first_column", result.columns[0].name)
        assertEquals("second_column", result.columns[1].name)
    }
}